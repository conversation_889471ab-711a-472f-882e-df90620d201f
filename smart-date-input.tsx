"use client"

import type React from "react"

import { useState } from "react"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"

interface SmartDateInputProps {
  label?: string
  value?: string
  onChange?: (value: string) => void
}

function SmartDateInput({ label, value = "", onChange }: SmartDateInputProps) {
  const [inputValue, setInputValue] = useState(value)

  const autoFixDate = (dateString: string): string => {
    if (!dateString) return ""

    const date = new Date(dateString)
    if (isNaN(date.getTime())) return ""

    const today = new Date()
    const currentYear = today.getFullYear()
    const maxFutureDate = new Date(today.getFullYear() + 2, 11, 31) // 2 years from now
    const minPastDate = new Date(today.getFullYear() - 1, 0, 1) // 1 year ago

    // Auto-fix unrealistic years
    if (date.getFullYear() > currentYear + 2) {
      // If year is way in future, assume they meant current year
      const fixedDate = new Date(currentYear, date.getMonth(), date.getDate())
      return fixedDate.toISOString().split("T")[0]
    }

    if (date.getFullYear() < currentYear - 1) {
      // If year is way in past, assume they meant current year
      const fixedDate = new Date(currentYear, date.getMonth(), date.getDate())
      return fixedDate.toISOString().split("T")[0]
    }

    // Constrain to reasonable range
    if (date > maxFutureDate) {
      return maxFutureDate.toISOString().split("T")[0]
    }

    if (date < minPastDate) {
      return minPastDate.toISOString().split("T")[0]
    }

    return dateString
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)
    onChange?.(newValue)
  }

  const handleBlur = () => {
    // Auto-fix when user leaves the field
    const fixed = autoFixDate(inputValue)
    if (fixed !== inputValue) {
      setInputValue(fixed)
      onChange?.(fixed)
    }
  }

  const today = new Date()
  const minDate = new Date(today.getFullYear() - 1, 0, 1).toISOString().split("T")[0]
  const maxDate = new Date(today.getFullYear() + 2, 11, 31).toISOString().split("T")[0]

  return (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      <Input
        type="date"
        value={inputValue}
        onChange={handleChange}
        onBlur={handleBlur}
        min={minDate}
        max={maxDate}
        className="w-full"
      />
      <p className="text-xs text-muted-foreground">Auto-corrects to reasonable dates (1 year ago to 2 years ahead)</p>
    </div>
  )
}

export default function Component() {
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")

  return (
    <div className="max-w-md mx-auto p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Smart Auto-Fixing Date Input</h2>
        <p className="text-sm text-muted-foreground mb-6">
          Try typing impossible dates like 11/12/33311 - they'll auto-fix when you tab away!
        </p>
      </div>

      <SmartDateInput label="Start Date" value={startDate} onChange={setStartDate} />

      <SmartDateInput label="End Date" value={endDate} onChange={setEndDate} />

      {(startDate || endDate) && (
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Selected:</h3>
          {startDate && <p>Start: {new Date(startDate).toLocaleDateString()}</p>}
          {endDate && <p>End: {new Date(endDate).toLocaleDateString()}</p>}
        </div>
      )}

      <div className="text-xs text-muted-foreground space-y-1">
        <p>
          <strong>Auto-fixes:</strong>
        </p>
        <ul className="list-disc list-inside ml-2">
          <li>Year 33311 → Current year</li>
          <li>Year 1990 → Current year</li>
          <li>Dates beyond 2 years → Max allowed date</li>
          <li>Dates before 1 year ago → Min allowed date</li>
        </ul>
      </div>
    </div>
  )
}
