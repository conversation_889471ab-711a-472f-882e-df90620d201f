"use client"

import type React from "react"

import { useState, useRef } from "react"
import { Label } from "@/components/ui/label"

interface MaskedDateInputProps {
  label?: string
  value?: string
  onChange?: (value: string) => void
}

function MaskedDateInput({ label, value = "", onChange }: MaskedDateInputProps) {
  const [inputValue, setInputValue] = useState(value)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const input = e.target.value.replace(/\D/g, "") // Only digits
    let formatted = ""

    // Format as user types
    for (let i = 0; i < input.length && i < 8; i++) {
      if (i === 2 || i === 4) {
        formatted += "/"
      }
      formatted += input[i]
    }

    setInputValue(formatted)
    onChange?.(formatted)
  }

  const handleKeyDown = (e: React.KeyEvent<HTMLInputElement>) => {
    // Allow backspace to work naturally
    if (e.key === "Backspace") {
      const cursorPos = (e.target as HTMLInputElement).selectionStart || 0
      if (cursorPos > 0 && inputValue[cursorPos - 1] === "/") {
        // Skip over the slash when backspacing
        const newValue = inputValue.slice(0, cursorPos - 2) + inputValue.slice(cursorPos)
        setInputValue(newValue)
        onChange?.(newValue)
        e.preventDefault()
      }
    }
  }

  // Create the mask display
  const mask = "MM/DD/YYYY"
  const displayValue = inputValue.padEnd(10, " ")

  return (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      <div className="relative">
        {/* Background mask */}
        <div className="absolute inset-0 flex items-center px-3 pointer-events-none text-muted-foreground font-mono text-sm">
          {mask.split("").map((char, index) => (
            <span key={index} className={`${index < inputValue.length ? "text-transparent" : ""}`}>
              {char}
            </span>
          ))}
        </div>

        {/* Actual input */}
        <input
          ref={inputRef}
          type="text"
          value={inputValue}
          onChange={handleChange}
          onKeyDown={handleKeyDown}
          maxLength={10}
          className="w-full px-3 py-2 font-mono text-sm border border-input bg-background rounded-md focus:outline-none focus:ring-2 focus:ring-ring focus:border-transparent relative z-10 bg-transparent"
          style={{ caretColor: "black" }}
        />
      </div>
    </div>
  )
}

export default function Component() {
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")

  return (
    <div className="max-w-md mx-auto p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Always-Visible Date Format</h2>
        <p className="text-sm text-muted-foreground mb-6">The MM/DD/YYYY guide stays visible as you type.</p>
      </div>

      <MaskedDateInput label="Start Date" value={startDate} onChange={setStartDate} />

      <MaskedDateInput label="End Date" value={endDate} onChange={setEndDate} />

      {(startDate || endDate) && (
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Entered:</h3>
          {startDate && <p>Start: {startDate}</p>}
          {endDate && <p>End: {endDate}</p>}
        </div>
      )}
    </div>
  )
}
