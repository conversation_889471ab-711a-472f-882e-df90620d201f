"use client"

import type React from "react"

import { useState, useRef, useEffect } from "react"
import { CalendarIcon } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Calendar } from "@/components/ui/calendar"

// Smart date parser that handles various formats
function parseFlexibleDate(input: string): Date | null {
  if (!input.trim()) return null

  const today = new Date()
  const currentYear = today.getFullYear()

  // Remove any non-digit characters except slashes and dashes
  const cleaned = input.replace(/[^\d/-]/g, "")

  // Handle various formats
  const patterns = [
    // MM/DD/YYYY or MM-DD-YYYY
    /^(\d{1,2})[/-](\d{1,2})[/-](\d{4})$/,
    // MM/DD or MM-DD (assume current year)
    /^(\d{1,2})[/-](\d{1,2})$/,
    // MMDD (assume current year)
    /^(\d{2})(\d{2})$/,
    // MMDDYY
    /^(\d{2})(\d{2})(\d{2})$/,
    // MMDDYYYY
    /^(\d{2})(\d{2})(\d{4})$/,
  ]

  for (const pattern of patterns) {
    const match = cleaned.match(pattern)
    if (match) {
      let month, day, year

      if (match.length === 3) {
        // MM/DD format
        month = Number.parseInt(match[1])
        day = Number.parseInt(match[2])
        year = currentYear
      } else if (match.length === 4) {
        month = Number.parseInt(match[1])
        day = Number.parseInt(match[2])
        year = match[3].length === 2 ? 2000 + Number.parseInt(match[3]) : Number.parseInt(match[3])
      }

      // Validate ranges
      if (month >= 1 && month <= 12 && day >= 1 && day <= 31) {
        const date = new Date(year, month - 1, day)
        // Check if the date is valid (handles invalid dates like Feb 30)
        if (date.getMonth() === month - 1 && date.getDate() === day) {
          return date
        }
      }
    }
  }

  return null
}

function formatDate(date: Date): string {
  return date.toLocaleDateString("en-US", {
    month: "2-digit",
    day: "2-digit",
    year: "numeric",
  })
}

interface SmartDateInputProps {
  label?: string
  placeholder?: string
  value?: Date
  onChange?: (date: Date | null) => void
}

function SmartDateInput({
  label,
  placeholder = "Type date (e.g., 1225, 12/25, 12/25/24)",
  value,
  onChange,
}: SmartDateInputProps) {
  const [inputValue, setInputValue] = useState("")
  const [isValid, setIsValid] = useState(true)
  const [showCalendar, setShowCalendar] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  useEffect(() => {
    if (value) {
      setInputValue(formatDate(value))
    }
  }, [value])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setInputValue(newValue)

    if (newValue.trim() === "") {
      setIsValid(true)
      onChange?.(null)
      return
    }

    const parsedDate = parseFlexibleDate(newValue)
    if (parsedDate) {
      setIsValid(true)
      onChange?.(parsedDate)
    } else {
      setIsValid(false)
    }
  }

  const handleKeyDown = (e: React.KeyEvent) => {
    // Allow quick shortcuts
    if (e.key === "Enter") {
      const parsedDate = parseFlexibleDate(inputValue)
      if (parsedDate) {
        setInputValue(formatDate(parsedDate))
        inputRef.current?.blur()
      }
    }

    // Today shortcut
    if (e.key === "t" && e.ctrlKey) {
      e.preventDefault()
      const today = new Date()
      setInputValue(formatDate(today))
      onChange?.(today)
    }
  }

  const handleCalendarSelect = (date: Date | undefined) => {
    if (date) {
      setInputValue(formatDate(date))
      onChange?.(date)
      setShowCalendar(false)
    }
  }

  return (
    <div className="space-y-2">
      {label && <Label>{label}</Label>}
      <div className="relative">
        <Input
          ref={inputRef}
          value={inputValue}
          onChange={handleInputChange}
          onKeyDown={handleKeyDown}
          placeholder={placeholder}
          className={`pr-10 ${!isValid ? "border-red-500 focus-visible:ring-red-500" : ""}`}
        />
        <Popover open={showCalendar} onOpenChange={setShowCalendar}>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-0 top-0 h-full px-3 hover:bg-transparent"
              type="button"
            >
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="end">
            <Calendar mode="single" selected={value} onSelect={handleCalendarSelect} initialFocus />
          </PopoverContent>
        </Popover>
      </div>
      {!isValid && <p className="text-sm text-red-500">Invalid date format. Try: 1225, 12/25, or 12/25/24</p>}
      <div className="text-xs text-muted-foreground">
        <strong>Quick formats:</strong> 1225 → 12/25/{new Date().getFullYear()}, 122524 → 12/25/24, Ctrl+T for today
      </div>
    </div>
  )
}

export default function Component() {
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)

  return (
    <div className="max-w-md mx-auto p-6 space-y-6">
      <div>
        <h2 className="text-2xl font-bold mb-4">Smart Date Input Demo</h2>
        <p className="text-sm text-muted-foreground mb-6">
          Type dates naturally with minimal keypresses. No need to navigate calendar widgets!
        </p>
      </div>

      <SmartDateInput label="Start Date" value={startDate} onChange={setStartDate} />

      <SmartDateInput label="End Date" value={endDate} onChange={setEndDate} />

      {(startDate || endDate) && (
        <div className="p-4 bg-muted rounded-lg">
          <h3 className="font-semibold mb-2">Selected Dates:</h3>
          {startDate && <p>Start: {formatDate(startDate)}</p>}
          {endDate && <p>End: {formatDate(endDate)}</p>}
        </div>
      )}

      <div className="text-sm text-muted-foreground space-y-1">
        <p>
          <strong>Supported formats:</strong>
        </p>
        <ul className="list-disc list-inside space-y-1 ml-2">
          <li>
            <code>1225</code> → December 25, {new Date().getFullYear()}
          </li>
          <li>
            <code>12/25</code> → December 25, {new Date().getFullYear()}
          </li>
          <li>
            <code>122524</code> → December 25, 2024
          </li>
          <li>
            <code>12/25/24</code> → December 25, 2024
          </li>
          <li>
            <code>12-25-2024</code> → December 25, 2024
          </li>
          <li>
            <code>Ctrl+T</code> → Today's date
          </li>
        </ul>
      </div>
    </div>
  )
}
